{"name": "@debutter/dough", "version": "0.5.0", "license": "MIT", "tasks": {"dev": "deno task web & deno test -A --parallel --trace-leaks", "build": "vite build --emptyOutDir", "fix": "deno lint && deno fmt && deno publish --allow-dirty --dry-run", "web": "vite", "publish": "deno task fix && deno publish"}, "imports": {"@std/assert": "jsr:@std/assert@1", "glob": "npm:glob@^11.0.2", "vite": "npm:vite@^6.3.5"}, "exports": "./src/index.ts", "compilerOptions": {"lib": ["dom", "dom.iterable", "dom.asynciterable", "deno.ns"]}, "publish": {"exclude": ["test/", "dist/", "public/", "static/"]}, "lint": {"exclude": ["./public/", "./static/", "./dist/"], "rules": {"tags": ["recommended"], "include": ["explicit-function-return-type", "explicit-module-boundary-types", "eqeqeq", "single-var-declarator", "verbatim-module-syntax"]}}, "fmt": {"exclude": ["./public/", "./static/", "./dist/"], "indentWidth": 4, "useTabs": false}, "nodeModulesDir": "auto"}